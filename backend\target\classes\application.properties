spring.application.name=Organik-Kose
# Server Port
server.port=8081

# MySQL Veritabani Bağlantisi
spring.datasource.url=*****************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Rabia.05
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate Ayarlari
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# JWT Ayarlari
jwt.secret=organikKoseSecretKeyForJWTTokenGenerationMustBeLongEnoughForHS256
jwt.expiration=86400000

# Circular References (geçici çözüm)
spring.main.allow-circular-references=true

# Swagger/OpenAPI Ayarlari
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true